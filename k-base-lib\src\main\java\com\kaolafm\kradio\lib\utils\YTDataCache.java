package com.kaolafm.kradio.lib.utils;

import android.util.ArrayMap;

import androidx.annotation.Nullable;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.kradio.lib.basedb.manager.BaseManager;
import com.kaolafm.kradio.lib.basedb.manager.Manager;
import com.kaolafm.kradio.lib.bean.BroadcastRadioSimpleData;
import com.kaolafm.kradio.lib.bean.HistoryItem;
import com.kaolafm.opensdk.api.maintab.MainTabRequest;
import com.kaolafm.opensdk.api.maintab.model.MainTabBean;
import com.kaolafm.opensdk.api.operation.OperationRequest;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

public class YTDataCache {

    private static final String TAG = "YTDataCache";

    private static CompletableFuture<List<MainTabBean>> ongoingTabBeansRequest = null;
    private static List<MainTabBean> tabBeansCache = null;

    private static final ArrayMap<String, List<ColumnGrp>> columnGrpCacheMap = new ArrayMap<>();
    private static final ArrayMap<String, CompletableFuture<List<ColumnGrp>>> ongoingColumnGrpRequestMap = new ArrayMap<>();

    private static List<HistoryItem> historyCache = null;
    private static CompletableFuture<List<HistoryItem>> ongoingHistoryRequest = null;

    private static List<BroadcastRadioSimpleData> localBroadcastCache = null;
    private static CompletableFuture<List<BroadcastRadioSimpleData>> ongoingLocalBroadcastRequest = null;

    private static int selectPage = 0;

    private static final AtomicBoolean prepareHome = new AtomicBoolean(false);
    private static final AtomicBoolean prepareHistory = new AtomicBoolean(false);
    //1 loadCache; 2 updateCache

    public static void clearCache() {
        ongoingTabBeansRequest = null;
        tabBeansCache = null;
        columnGrpCacheMap.clear();
        ongoingColumnGrpRequestMap.clear();
        historyCache = null;
        ongoingHistoryRequest = null;
        localBroadcastCache = null;
        ongoingLocalBroadcastRequest = null;
        selectPage = 0;
        prepareHome.set(false);
        prepareHistory.set(false);
    }

    public static void prepareHomeData() {
        if (prepareHome.compareAndSet(false, true)) {
            //这里只发网络请求，原始数据从RequestInterceptManager拦截器里截取
            YTLogUtil.logStart(TAG, "prepareHomeData", "start");

            fetchTabList();
        }
    }

    /**
     * 重置prepareHome标志，允许重新加载首页数据
     * 主要用于Activity重建时确保数据能够重新加载
     */
    public static void resetPrepareHomeFlag() {
        prepareHome.set(false);
        YTLogUtil.logStart(TAG, "resetPrepareHomeFlag", "prepareHome flag reset to false");
    }

    public static void prepareHistoryData(@Nullable BaseManager historyManager) {
        if (prepareHistory.compareAndSet(false, true)) {
            YTLogUtil.logStart(TAG, "prepareHistoryData", "start");
            obtainHistoryList(historyManager);
            obtainHistoryLocalBroadcastList(historyManager);
        }
    }
//    public static @Nullable List<MainTabBean> getCachedTabList(){
//        return tabBeansCache;
//    }

    public static void setCachedTabList(List<MainTabBean> tabs) {
        if (!ListUtil.isEmpty(tabs)) {
            tabBeansCache = tabs;
        }
    }

//    public static @Nullable String getFirstCachedTabZone(){
//        if(tabBeansCache == null){
//            return null;
//        }
//
//        if(tabBeansCache.isEmpty()){
//            return null;
//        }
//
//        if(tabBeansCache.get(0).getLinkZone() == null){
//            return null;
//        }
//
//        YTLogUtil.logStart(TAG, "getData", "getFirstCachedTabZone zone = "+tabBeansCache.get(0).getLinkZone());
//        return tabBeansCache.get(0).getLinkZone();
//    }

    private static void calculateOriginSelected(List<MainTabBean> mainTabBeans) {
        if (mainTabBeans == null || mainTabBeans.isEmpty()) {
            return;
        }
        for (int i = 0; i < mainTabBeans.size(); i++) {
            if (mainTabBeans.get(i).getIsLandingPage() == 1) {
                selectPage = i;
            }
        }
    }


    public static CompletableFuture<List<MainTabBean>> fetchTabList() {
        YTLogUtil.logStart(TAG, "fetchTabList", "start");
        if (ongoingTabBeansRequest != null && !ongoingTabBeansRequest.isDone()) {
            return ongoingTabBeansRequest;
        }
        ongoingTabBeansRequest = new CompletableFuture<>();
        if (tabBeansCache != null && !tabBeansCache.isEmpty()) {
            ongoingTabBeansRequest.complete(tabBeansCache);
            return ongoingTabBeansRequest;
        }
        new MainTabRequest().getMainTab(new HttpCallback<List<MainTabBean>>() {
            @Override
            public void onSuccess(List<MainTabBean> mainTabBeans) {
                YTLogUtil.logStart(TAG, "fetchTabList", "success");
                if (mainTabBeans != null && !mainTabBeans.isEmpty()) {
                    tabBeansCache = mainTabBeans;
                    calculateOriginSelected(mainTabBeans);
                    ongoingTabBeansRequest.complete(mainTabBeans);
                    fetchHomePageZone(mainTabBeans.get(selectPage).getLinkZone());
                } else {
                    ongoingTabBeansRequest.complete(null);
                }
            }

            @Override
            public void onError(ApiException e) {
                YTLogUtil.logStart(TAG, "failure", e.toString());
                ongoingTabBeansRequest.completeExceptionally(e);
            }
        });
        return ongoingTabBeansRequest;
    }

    public static int getSelectPage() {
        return selectPage;
    }

    public static void setSelectPage(int page) {
        selectPage = page;
    }

//    public static List<ColumnGrp> getCachedColumnGrpList(){
//        return columnGrpsCache;
//    }

//    public static void fetchSelectedZone(){
//        fetchHomePageZone(tabBeansCache.get(selectPage).getLinkZone());
//    }

    public static CompletableFuture<List<ColumnGrp>> fetchHomePageZone(String zone) {
        YTLogUtil.logStart(TAG, "fetchHomePageZone", "start：" + zone);
        CompletableFuture<List<ColumnGrp>> ongoingRequest = ongoingColumnGrpRequestMap.get(zone);
        if (ongoingRequest != null && !ongoingRequest.isDone()) {
            return ongoingRequest;
        }
        ongoingRequest = new CompletableFuture<>();
        if (zone == null) {
            ongoingRequest.complete(null);
            return ongoingRequest;
        }
        if (columnGrpCacheMap.containsKey(zone)) {
            YTLogUtil.logStart(TAG, "fetchHomePageZone", "end：" + zone);
            ongoingRequest.complete(columnGrpCacheMap.get(zone));
            return ongoingRequest;
        }
        ongoingColumnGrpRequestMap.put(zone, ongoingRequest);

        new OperationRequest().getColumnTree(true, zone, new HttpCallback<List<ColumnGrp>>() {
            @Override
            public void onSuccess(List<ColumnGrp> grps) {
                // 只有当数据不为空时才缓存，避免缓存空数据
                if (grps != null && !grps.isEmpty()) {
                    columnGrpCacheMap.put(zone, grps);
                }
                YTLogUtil.logStart(TAG, "fetchHomePageZone", "success：" + zone);
                CompletableFuture<List<ColumnGrp>> ongoingRequest = ongoingColumnGrpRequestMap.get(zone);
                if (ongoingRequest != null) {
                    ongoingRequest.complete(grps);
                }
            }

            @Override
            public void onError(ApiException e) {
                YTLogUtil.logStart(TAG, "fetchHomePageZone", "error：" + e.getMessage());
                CompletableFuture<List<ColumnGrp>> ongoingRequest = ongoingColumnGrpRequestMap.get(zone);
                if (ongoingRequest != null) {
                    ongoingRequest.completeExceptionally(e);
                }
            }
        });
        return ongoingRequest;
    }

    public static CompletableFuture<List<HistoryItem>> obtainHistoryList(Manager historyManager) {
        YTLogUtil.logStart(TAG, "obtainHistoryList", "start");
//        if (ongoingHistoryRequest != null && !ongoingHistoryRequest.isDone()) {
//            return ongoingHistoryRequest;
//        }
        if (ongoingHistoryRequest != null) {
            YTLogUtil.logStart(TAG, "obtainHistoryList", "end");
            ongoingHistoryRequest.complete(historyCache);
            return ongoingHistoryRequest;
        }
        ongoingHistoryRequest = new CompletableFuture<>();
//        if (historyCache != null) {
//            ongoingHistoryRequest.complete(historyCache);
//            return ongoingHistoryRequest;
//        }
        historyManager.getHistoryList(new HttpCallback<List<HistoryItem>>() {
            @Override
            public void onSuccess(List<HistoryItem> historyList) {
                YTLogUtil.logStart(TAG, "obtainHistoryList", "success");
                historyCache = historyList;
                ongoingHistoryRequest.complete(historyList);
            }

            @Override
            public void onError(ApiException e) {
                ongoingHistoryRequest.complete(null);
            }
        });
        return ongoingHistoryRequest;
    }

    public static CompletableFuture<List<BroadcastRadioSimpleData>> obtainHistoryLocalBroadcastList(BaseManager historyManager) {
        YTLogUtil.logStart(TAG, "obtainHistoryLocalBroadcastList", "start");
//        if (ongoingLocalBroadcastRequest != null && !ongoingLocalBroadcastRequest.isDone()) {
//            return ongoingLocalBroadcastRequest;
//        }
        if (ongoingLocalBroadcastRequest != null) {
            YTLogUtil.logStart(TAG, "obtainHistoryLocalBroadcastList", "end");
            ongoingLocalBroadcastRequest.complete(localBroadcastCache);
            return ongoingLocalBroadcastRequest;
        }
//        if (localBroadcastCache != null) {
//            ongoingLocalBroadcastRequest = new CompletableFuture<>();
//            ongoingLocalBroadcastRequest.complete(localBroadcastCache);
//            return ongoingLocalBroadcastRequest;
//        }

        ongoingLocalBroadcastRequest = CompletableFuture.supplyAsync(() -> {
            List<BroadcastRadioSimpleData> localBroadcastList =
                    historyManager.getLocalBroadcastListDirectly();
            localBroadcastCache = localBroadcastList;
            YTLogUtil.logStart(TAG, "obtainHistoryLocalBroadcastList", "success");
            return localBroadcastList;
        });
        return ongoingLocalBroadcastRequest;
    }
}
