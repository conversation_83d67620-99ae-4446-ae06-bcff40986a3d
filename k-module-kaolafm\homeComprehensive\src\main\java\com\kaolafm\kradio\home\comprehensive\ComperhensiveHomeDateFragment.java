package com.kaolafm.kradio.home.comprehensive;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;

import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.kradio.common.KradioSDKManager;
import com.kaolafm.kradio.common.SkinStateManager;
import com.kaolafm.kradio.common.widget.GridRecyclerView;
import com.kaolafm.kradio.component.ui.base.cell.HomeCell;
import com.kaolafm.kradio.component.ui.brandpage.ComponentBrandPageHomeCardCell;
import com.kaolafm.kradio.config.ConfigSettingManager;
import com.kaolafm.kradio.home.comprehensive.data.EventOrientationChangeData;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerBar;
import com.kaolafm.kradio.k_kaolafm.BuildConfig;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.router.RouterManager;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.activity.comprehensive.ui.ActivitysDetailsDialogFragment;
import com.kaolafm.kradio.component.ui.base.utils.ComponentUtils;
import com.kaolafm.kradio.home.comprehensive.adapter.HomeAdapter;
import com.kaolafm.kradio.component.ui.k_1x1card.ComponentBrandPage1And1Cell;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBrandPageBigCardCell;
import com.kaolafm.kradio.component.ui.topiccard.ComponentTopicCardCell;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.home.comprehensive.ad.KradioAdColumnManager;
import com.kaolafm.kradio.home.comprehensive.gallery.PageJumper;
import com.kaolafm.kradio.component.ui.k_1x1card.Component1And1Cell;
import com.kaolafm.kradio.component.ui.k_2x1card.Component2And1Cell;
import com.kaolafm.kradio.component.ui.k_2x3card.Component2And3Cell;
import com.kaolafm.kradio.component.ui.activitycard.ComponentActivityCell;
import com.kaolafm.kradio.component.ui.bigcard.ComponentBigCardCell;
import com.kaolafm.kradio.component.ui.rotationcard.ComponentRotationCell;
import com.kaolafm.kradio.home.comprehensive.item.FunctionPairCell;
import com.kaolafm.kradio.home.comprehensive.mvp.HomeListPresenter;
import com.kaolafm.kradio.home.comprehensive.mvp.IHomeListView;
import com.kaolafm.kradio.home.comprehensive.playerbar.ComprehensivePlayerHelper;
import com.kaolafm.kradio.home.comprehensive.recyclerview.HomeRecyclerViewHelper;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAuthInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioClickRetryInter;
import com.kaolafm.kradio.lib.base.ui.BaseAdapter;
import com.kaolafm.kradio.lib.base.ui.BaseViewPagerLazyFragment;
import com.kaolafm.kradio.lib.common.ResType;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ClazzUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.RecyclerViewExposeUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.StringUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.YTLogUtil;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.router.RouterDesturlPlayCallbackImpl;
import com.kaolafm.launcher.FirstDataLoadedEvent;
import com.kaolafm.launcher.LauncherActivity;
import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.config.ConfigSettingOption;
import com.kaolafm.opensdk.api.config.IConfigSettingOptionListener;
import com.kaolafm.opensdk.api.operation.model.column.ActivityDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.ColumnContent;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.TopicDetailColumnMember;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.lcodecore.tkrefreshlayout.utils.ScrollingUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页列表展示
 * 蔡佳彬
 */
public class ComperhensiveHomeDateFragment extends BaseViewPagerLazyFragment<HomeListPresenter>
        implements IHomeListView, RecyclerViewExposeUtil.OnItemExposeListener {

    private static final String TAG = "ComperhensiveHomeDateFragment";
    public static final String ARGUMENT_PAGE_ID = "ARGUMENT_PAGE_ID";

    LinearLayout home_root_cl;
    View mPcLoadingView;
    ViewStub mVsHomeNoNetwork;

    View mHomeNoNetWorkRl;
    TextView mScrollLeft, mScrollRight;

    private String zone = "";
    private final List<RecyclerViewExposeUtil> mExposeRvUtilList = new ArrayList<>();
    private KRadioAuthInter mKRadioAuthInter;
    public List<HomeAdapter> mHomeAdapterList = new ArrayList<>();
    private SkinStateManager.ILoadSkinListener loadSkinListener;
    private final List<HomeRecyclerViewHelper> mHomeRecyclerViewHelperList = new ArrayList<>();
    private List<HomeCell> mHomeCells = new ArrayList<>();
    private final List<GridRecyclerView> recyclerViewList = new ArrayList<>();
    private final List<HomeCell> brandPageCell = new ArrayList<>();
    private String destUrl;//跳转地址

    private final RouterDesturlPlayCallbackImpl mRouterDesturlPlayCallbackImpl = new RouterDesturlPlayCallbackImpl();
    public ComperhensiveHomeDateFragment() {
        // Required empty public constructor
    }

    @Override
    protected boolean isReportFragment() {
        return true;
    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_MAIN;
    }

    public static ComperhensiveHomeDateFragment newInstance(String pageId, String tag) {
        ComperhensiveHomeDateFragment fragment = new ComperhensiveHomeDateFragment();
        fragment.setMTag(tag);
        Bundle arg = new Bundle();
        arg.putString(ARGUMENT_PAGE_ID, pageId);
        arg.putString("zone", tag); // 保存zone参数到Bundle中，确保Activity重建时不丢失
        fragment.setArguments(arg);
        return fragment;
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        try {
            mKRadioAuthInter = (KRadioAuthInter) ClazzUtil
                    .invoke(Class.forName(StringUtil.join(ClazzImplUtil.CLASS_FATHER_PACKAGE, "KRadioAuthImpl")),
                            null, "getInstance", new Object[]{});
        } catch (Exception e) {
            //TODO：Wangyi 有点问题
        }
    }

    @Override
    public boolean useEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventOrientationChange(EventOrientationChangeData eventOrientationChangeData){
        Log.e("ComperhensiveHomeDateFM", "onEventOrientationChange");
        if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            for (HomeAdapter homeAdapter : mHomeAdapterList) {
                homeAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mHomeAdapterList.size() > 1) {
            mHomeAdapterList.get(0).notifyDataSetChanged();
        }
        // CPU优化：Fragment可见时恢复图片请求
        Glide.with(this).resumeRequests();
    }

    @Override
    public void onStop() {
        super.onStop();
        // CPU优化：Fragment不可见时暂停图片请求
        Glide.with(this).pauseRequests();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 从Bundle中恢复zone参数，确保Activity重建时不丢失
        Bundle args = getArguments();
        if (args != null && zone == null) {
            zone = args.getString("zone", "");
            Log.i(TAG, "onCreate: restored zone = " + zone);
        }

        loadSkinListener = () -> {
            if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
                for (HomeAdapter homeAdapter : mHomeAdapterList) {
                    homeAdapter.notifyDataSetChanged();
                }
            }
        };
        SkinStateManager.getInstance().addLoadSkinListener(loadSkinListener);
    }

    @Override
    public void initArgs() {
        super.initArgs();
    }

    /**
     * 加载数据
     *
     * @param zone
     */
    public void loadDate(String zone) {
        this.zone = zone;
    }

    private boolean isShowDate;

    public void setPlayEnd() {
        if (isShowDate)
            hideLoading();
    }

    /**
     * 设置播放状态
     */
    public void setSelected() {
        if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            for (HomeAdapter homeAdapter : mHomeAdapterList) {
                ThreadUtil.runOnUI(homeAdapter::changePlayingState);
            }
        }
        jumpPage();
    }

    public synchronized void jumpPage() {
        if (isShowDate)
            hideLoading();
        if (!TextUtils.isEmpty(destUrl)) {
            RouterManager.getInstance().interceptApplicationJumpEvent(getContext(), destUrl, mRouterDesturlPlayCallbackImpl);
            destUrl = "";
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_comperhensive_home_date;
    }

    @Override
    protected int getLayoutId_Tow() {
        return 0;
    }

    @Override
    protected HomeListPresenter createPresenter() {
        return new HomeListPresenter(this);
    }

    @Override
    protected void lazyLoad() {
        // 在Activity重建时，确保清理可能的脏数据缓存
        // 避免显示错误栏目的内容
        clearPreviousDataIfNeeded();

        // 确保zone不为空，如果为空则使用默认值
        if (zone == null || zone.isEmpty()) {
            zone = "HomePage"; // 使用默认的首页zone
            Log.w(TAG, "lazyLoad: zone is empty, using default HomePage");
        }

        Log.i(TAG, "lazyLoad: initData with zone = " + zone);
        mPresenter.initData(zone);
    }

    /**
     * 清理可能的脏数据缓存，解决多屏切换时数据错乱问题
     */
    private void clearPreviousDataIfNeeded() {
        // 简单有效的方案：在每次lazyLoad时检查是否有旧数据
        // 如果有旧数据且不是当前应该显示的数据，则清理
        if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            boolean hasData = false;
            for (HomeAdapter adapter : mHomeAdapterList) {
                if (adapter.getItemCount() > 0) {
                    hasData = true;
                    break;
                }
            }

            // 如果已经有数据，但是Fragment刚刚变为可见（可能是Activity重建），
            // 为了安全起见，清理旧数据，让新数据重新加载
            if (hasData && !isLoaded) {
                Log.i(TAG, "clearPreviousDataIfNeeded: Found existing data in newly visible fragment, clearing to avoid stale data");
                clearDisplayData();
            }
        }
    }

    /**
     * 清理显示的数据
     */
    private void clearDisplayData() {
        if (mHomeAdapterList != null) {
            for (HomeAdapter adapter : mHomeAdapterList) {
                adapter.clear(); // 使用BaseAdapter的clear方法
            }
        }
        if (recyclerViewList != null) {
            for (RecyclerView recyclerView : recyclerViewList) {
                if (recyclerView.getAdapter() != null) {
                    recyclerView.getAdapter().notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    public void initView(View view) {
        home_root_cl=view.findViewById(R.id.home_root_cl);
        home_root_cl.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
        mPcLoadingView=view.findViewById(R.id.pc_loading);
        mVsHomeNoNetwork=view.findViewById(R.id.vs_home_no_network);
        mScrollLeft = view.findViewById(R.id.cd_left);
        mScrollRight = view.findViewById(R.id.cd_right);
        PlayerManager.getInstance().addGeneralListener(generalListener);
    }

    private void initListDate(GridRecyclerView mHomeRecyclerView) {
        HomeRecyclerViewHelper mHomeRecyclerViewHelper = new HomeRecyclerViewHelper();
        mHomeRecyclerViewHelperList.add(mHomeRecyclerViewHelper);
        HomeAdapter mHomeAdapter = new HomeAdapter();
        mHomeAdapterList.add(mHomeAdapter);
        mHomeAdapter.setOnItemClickListener(new BaseAdapter.OnItemClickListener<HomeCell>() {
            @Override
            public void onItemClick(View view, int viewType, HomeCell homeCell, int position) {
                onListItemClick(view, homeCell, position);
            }
        });
        mHomeRecyclerViewHelper.bindRecycleView(mHomeRecyclerView, mHomeAdapter);

        RecyclerViewExposeUtil mExposeRvUtil = new RecyclerViewExposeUtil();
        mExposeRvUtilList.add(mExposeRvUtil);
        mExposeRvUtil.setRecyclerItemExposeListener(mHomeRecyclerView, this);
        KradioAdColumnManager.getInstance().bindHomeRecyclerViewHelper(mHomeRecyclerViewHelper);
    }

    /**
     * 打开组件活动详情
     *
     * @param homeCell
     */
    private void jumpToActivityDateils(HomeCell homeCell, int childposition) {
        //活动类型的点击
        ActivityDetailColumnMember activityDetailColumnMember = null;
        if (homeCell.getContentList().get(childposition) instanceof ActivityDetailColumnMember) {
            activityDetailColumnMember = (ActivityDetailColumnMember) homeCell.getContentList().get(childposition);
        }
        if (!activityDetailColumnMember.getActivityType().equals("1")) {
            return;
        }
        if (!TextUtils.isEmpty(activityDetailColumnMember.getActivityType())
                && activityDetailColumnMember.getActivityStatus().equals("1")) {
            //不是常驻的活动才可以打开详情
            String id = homeCell.getContentList().get(childposition).getId();
            Context context = getContext();
            if (context == null) {
                context = AppDelegate.getInstance().getContext();
            }
            if (TextUtils.isEmpty(id)) {
                ToastUtil.showInfo(context, "数据错误！");
                return;
            }
            ActivitysDetailsDialogFragment dialogFragment
                    = new ActivitysDetailsDialogFragment(context, id);
            dialogFragment.show();

        }
    }

    /**
     * Item的点击事件处理。这里有渠道使用到了AOP进行鉴权拦截。
     * do 首页 item 点击事件处理
     */
    @SuppressLint("LongLogTag")
    private void onListItemClick(View view1, HomeCell homeCell, int position) {
        //点击事件上报
        if (homeCell instanceof ComponentActivityCell) {
            jumpToActivityDateils(homeCell, 0);
            reportContentClickEvent(homeCell, 0);
        } else if (homeCell instanceof ComponentBrandPageHomeCardCell) {
            reportContentClickEvent(homeCell, 0);
            if (!NetworkUtil.isNetworkAvailable(AppDelegate.getInstance().getContext(), true)) {
                ToastUtil.showInfo(getContext(), ResUtil.getString(R.string.network_nosigin));
                return;
            }

            ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
                @Override
                public void onGetSuccess(ConfigSettingOption configSettingOption) {
                    if (configSettingOption.getShowBrandEntries() == 1 && !TextUtils.isEmpty(configSettingOption.getBrandPageId() + "")) {
                        Bundle bundle = new Bundle();
                        bundle.putString("brandPageId", configSettingOption.getBrandPageId() + "");
                        int enterAnim = 0, exitAnim = 0;
                        if (configSettingOption.getIsPlayAnim() == 1) {
                            //开启转场动画
                            enterAnim = R.anim.anim_jump_brand_enter;
                            exitAnim = R.anim.anim_jump_brand_exit;
                        }
                        AdvertisingManager.getInstance().close();
                        RouterManager.getInstance().jumpPageWithTransition(getActivity(), RouterConstance.BRAND_COMPREHENSIVE_URL, bundle, enterAnim, exitAnim);
                    }
                }

                @Override
                public void onGetFailure(ApiException e) {

                }
            });

        } else if (homeCell instanceof Component1And1Cell || homeCell instanceof Component2And1Cell
                || homeCell instanceof Component2And3Cell || homeCell instanceof ComponentRotationCell
                || homeCell instanceof ComponentBigCardCell || homeCell instanceof ComponentBrandPage1And1Cell
                || homeCell instanceof ComponentBrandPageBigCardCell || homeCell instanceof ComponentTopicCardCell) {
            if (view1.getTag() == null) {
                return;
            }
            int childposition = (int) view1.getTag();

            if (homeCell.contentList.size() > childposition && childposition >= 0) {
                reportContentClickEvent(homeCell, childposition);
                //跳转
                if (!TextUtils.isEmpty(homeCell.contentList.get(childposition).getDestUrl())) {
                    if (homeCell.contentList.get(childposition) instanceof ActivityDetailColumnMember) {
                        //活动类型
                        ActivityDetailColumnMember activityDetailColumnMember = null;
                        if (homeCell.getContentList().get(childposition) instanceof ActivityDetailColumnMember) {
                            activityDetailColumnMember = (ActivityDetailColumnMember) homeCell.getContentList().get(childposition);
                        }
                        //如果活动类型是常驻或者已下线就不能跳转
                        if (activityDetailColumnMember == null
                                || activityDetailColumnMember.getActivityType().equals("0")
                                || !activityDetailColumnMember.getActivityStatus().equals("1")) {
                            return;
                        }
                        RouterManager.getInstance().interceptApplicationJumpEvent(getContext(),
                                homeCell.contentList.get(childposition).getDestUrl(), mRouterDesturlPlayCallbackImpl);
                    } else if (homeCell.contentList.get(childposition) instanceof TopicDetailColumnMember) {
                        //话题类型
                        RouterManager.getInstance().interceptApplicationJumpEvent(getContext(),
                                homeCell.contentList.get(childposition).getDestUrl(), mRouterDesturlPlayCallbackImpl);
                    } else {
                        if (homeCell.contentList.get(childposition).getResType() == ResType.NOACTION_TYPE) {
                            RouterManager.getInstance().interceptApplicationJumpEvent(getContext(),
                                    homeCell.contentList.get(childposition).getDestUrl(), mRouterDesturlPlayCallbackImpl);
                            return;
                        }
                        homeCell.playId = Long.parseLong(homeCell.contentList.get(childposition).getId());
                        homeCell.resType = homeCell.contentList.get(childposition).getResType();

                        if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                            destUrl = homeCell.contentList.get(childposition).getDestUrl();
                            jumpPage();
                            return;
                        }
                        showLoading();
                        ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                        destUrl = homeCell.contentList.get(childposition).getDestUrl();
                        clickToPlay(homeCell);
                    }
                    return;
                } else {
                    if (homeCell.contentList.get(childposition) instanceof LiveProgramDetailColumnMember) {
                        //直播类型
                        if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                            Log.i(TAG, "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                            //如果流量鉴权不通过，进行流量鉴权的check,不要直接跳转到直播界面
                            mKRadioAuthInter.doCheckAuth(homeCell, KRadioAuthInter.METHOD_LIVING, true);
                        } else {
                            clickToPlayLive(homeCell, childposition);
                        }
                        return;
                    } else if (homeCell.contentList.get(childposition) instanceof ActivityDetailColumnMember) {
                        //活动类型
                        jumpToActivityDateils(homeCell, childposition);
                        return;
                    } else if (homeCell.contentList.get(childposition).getResType() == ResType.VIDEO_ALBUM_TYPE) {
                        PageJumper.getInstance().jumpToVideoAlbumFragment(homeCell.getContentList().get(childposition).getId());
                        return;
                    } else if (homeCell.contentList.get(childposition).getResType() == ResType.VIDEO_TYPE) {
                        ColumnContent content = homeCell.getContentList().get(childposition);
                        Map<String, String> map = new HashMap<>();
                        if(PlayerManager.getInstance().getSpeedLimitState()){
                            ToastUtil.showInfo(view1.getContext(), R.string.video_player_overspeed_toast);
                            PlayerManagerHelper.getInstance().start(String.valueOf(content.getId()), ResType.VIDEO_TYPE, true);
                        } else {
                            map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_TYPE, String.valueOf(ResType.VIDEO_TYPE));
                            map.put(Constants.ROUTER_PARAMS_KEY_RESOURCE_ID, content.getId());
                            RouterManager.getInstance().navigateToPage(view1.getContext(), Constants.PAGE_ID_VIDEO, map);
                        }
                        return;
                    }
//                    else if (homeCell.contentList.get(childposition) instanceof TopicDetailColumnMember) {
//                        //话题类型
//                        jumpToTopicDateils(homeCell, childposition);
//                        return;
//                    }
                }
                if (homeCell.contentList.get(childposition).getResType() == ResType.NOACTION_TYPE) {
                    return;// ResType.NOACTION_TYPE
                }
                homeCell.playId = Long.parseLong(homeCell.contentList.get(childposition).getId());
                homeCell.resType = homeCell.contentList.get(childposition).getResType();
                Log.i(TAG, "click start : id = " + homeCell.playId);
                if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                    Log.i(TAG,
                            "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                    //如果流量鉴权不通过，也要去触发播放
                    clickToPlay(homeCell);
                } else {
                    if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                        handleClickCurPlayItem(homeCell);
                        return;
                    }
                    ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                    clickToPlay(homeCell);
                }
                Log.i(TAG, "click end : id = " + homeCell.playId);

            }
        } else if (homeCell instanceof FunctionPairCell) {
            Object tag = view1.getTag();
            if (tag instanceof FunctionPairCell) {
                jumpToAllCategoriesFragment(0, Long.parseLong(((FunctionPairCell) tag).firstCode),
                        Long.parseLong(((FunctionPairCell) tag).secondCode));
            }
        } else {
            Log.i(TAG, "click start : id = " + homeCell.playId);
            if (mKRadioAuthInter != null && !mKRadioAuthInter.authStatus()) {
                Log.i(TAG,
                        "onCardClick: mKRadioAuthInter.authStatus() = " + mKRadioAuthInter.authStatus());
                //如果流量鉴权不通过，也要去触发播放
                clickToPlay(homeCell);
            } else {
                if (PlayerManagerHelper.getInstance().isPlayCurrentRadio(String.valueOf(homeCell.playId))) {
                    handleClickCurPlayItem(homeCell);
                    return;
                }
                ReportUtil.addRecommendSelectEvent(homeCell.outputMode, homeCell.callBack);
                clickToPlay(homeCell);
            }
            Log.i(TAG, "click end : id = " + homeCell.playId);
        }
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        recoverHomeData();
    }

    private void recoverHomeData() {
        Logging.i(TAG, "recoverHomeData start");
    }

    /**
     * 跳转到全部分类
     */
    private void jumpToAllCategoriesFragment(int type, long id, long secondId) {
        EventBus.getDefault().post(new JumpToFragentEvent(type, id, secondId));
    }

    @SuppressLint("LongLogTag")
    private void clickToPlay(HomeCell homeCell) {
        Log.i("handleClickCurPlayItem", "ComperhensiveHomeDateFragment -> clickToPlay");
        if (NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
            ComprehensivePlayerHelper.play(homeCell);
        } else {
            Log.i(TAG, "clickToPlay no Network");
        }
    }

    @SuppressLint("LongLogTag")
    private void clickToPlayLive(HomeCell homeCell, int childposition) {
        if (homeCell == null) {
            return;
        }
        Log.i(TAG, "homeCell id = " + homeCell.getContentList().get(childposition).getId()
                + "   " + homeCell.getContentList().get(childposition).getResType());
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (PlayerManagerHelper.getInstance().isSameProgram(playItem, String.valueOf(homeCell.getContentList().get(childposition).getId()))) {
            //相同 只跳转到直播间
            // 跳转直播间
            PageJumper.getInstance().jumpToLivePage(Long.parseLong(homeCell.getContentList().get(childposition).getId()));
            return;
        }
        if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
            return;
        }
        PlayerManagerHelper.getInstance().start(String.valueOf(homeCell.getContentList().get(childposition).getId())
                , homeCell.getContentList().get(childposition).getResType());
        PageJumper.getInstance().jumpToLivePage(Long.parseLong(homeCell.getContentList().get(childposition).getId()));
    }

    private void reportContentClickEvent(HomeCell homeCell, int childposition) {
        int action = ComponentUtils.getInstance().getContentAction(homeCell.getContentList().get(childposition).getDestUrl()
                , homeCell.getContentList().get(childposition).getCanPlay());

        String tag = getReportTag(homeCell, childposition);
        ReportUtil.addComponentShowAndClickEvent(homeCell.getContentList().get(childposition).getId(),
                true, ComponentUtils.getInstance().getComponentReportCardId(homeCell.getComponentType() + "")
                , action, homeCell.getColumnId(), homeCell.getPositionInParent() + "", childposition + "", homeCell.getContentList().get(childposition).getId()
                , tag, Constants.PAGE_ID_MAIN, tag);
    }

    private String getReportTag(HomeCell homeCell, int childposition) {
        String tag = "无";

        AlbumDetailColumnMember albumDetailColumnMember = null;
        if (homeCell.contentList.get(childposition) instanceof AlbumDetailColumnMember) {
            albumDetailColumnMember = (AlbumDetailColumnMember) homeCell.contentList.get(childposition);
            if (!TextUtils.isEmpty(albumDetailColumnMember.getFine() + "") && albumDetailColumnMember.getFine() == 1) {
                tag = "精品";
            } else if (albumDetailColumnMember.getVip() == 1) {
                tag = "VIP";
            }
        }
        return tag;
    }

    private void reportContentShowEvent(HomeCell homeCell) {
        if (homeCell == null) {
            return;
        }
        for (int i = 0; i < homeCell.getContentList().size(); i++) {
            int action = ComponentUtils.getInstance().getContentAction(homeCell.getContentList().get(i).getDestUrl()
                    , homeCell.getContentList().get(i).getCanPlay());
            ReportUtil.addComponentShowAndClickEvent(homeCell.getContentList().get(i).getId(),
                    false, ComponentUtils.getInstance().getComponentReportCardId(homeCell.getComponentType() + "")
                    , action, homeCell.getCode(), homeCell.getPositionInParent() + ""
                    , i + "", homeCell.getContentList().get(i).getId()
                    , getReportTag(homeCell, i), Constants.PAGE_ID_MAIN, getReportTag(homeCell, i));
        }
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
//        //内容recycleview的右边间距。横屏为零，竖屏为30
//        int contentRight = ResUtil.getDimen(R.dimen.home_content_padding_right);
//        int contentBottom = ResUtil.getDimen(R.dimen.home_content_padding_bottom);
//        //竖屏的时候top的padding为零，因为在HomeItemDecoration中做了便宜。
//        int contentTop = ResUtil.getDimen(R.dimen.home_content_padding_top);
//
//        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
//        if (inter != null && inter.isNeedReset()) {
//            contentBottom = ResUtil.getDimen(R.dimen.y35);
//            contentTop = ResUtil.getDimen(R.dimen.y10);
//        }
    }

    private final IGeneralListener generalListener = new IGeneralListener() {

        @Override
        public void getPlayListError(PlayItem playItem, int code, int i1) {
            Logging.i(TAG, "getPlayListError:" + code);
            hideLoading();
            switch (code) {
                case PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE:
                    break;
                case PlayerConstants.ERROR_CODE_RADIO_COPYRIGHT_LITE:
                    UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), R.string.comprehensive_radio_is_lite));
                    break;
                default:
                    if (playItem != null && playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING)
                        return;
                    UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), R.string.play_failed_str));
                    break;
            }
            if (isPlayBroadcastError()) {
                if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
                    for (HomeAdapter homeAdapter : mHomeAdapterList) {
                        homeAdapter.changeBroadcastPlayingState();
                    }
                }
            }
        }

        @Override
        public void playUrlError(int code) {
            Logging.i(TAG, "playUrlError code = " + code);
            hideLoading();
            if (!NetworkUtil.isNetworkAvailable(getContext(), false)) {
                UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), com.kaolafm.kradio.lib.R.string.no_net_work_str));
                return;
            }
            //fixed 修复最后一条播放完毕后，提醒"资源不支持的问题"
            if (code != PlayerConstants.ERROR_CODE_PLAY_LIST_IS_LAST_ONE) {
                UIThreadUtil.runUIThread(() -> ToastUtil.showError(getContext(), R.string.play_failed_str));
            }
        }
    };

    private boolean isPlayBroadcastError() {
        PlayItem playItemTemp = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItemTemp.getType() != PlayerConstants.RESOURCES_TYPE_INVALID) {
            return false;
        }
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        return true;
    }

    @Override
    public void onDestroy() {
        // 在调用super.onDestroy()之前先处理presenter相关的清理工作
        // 因为基类会在super.onDestroy()中将mPresenter置为null
        if (mPresenter != null) {
            mPresenter.cancelSchedule();
        }

        // 清理其他资源
        SkinStateManager.getInstance().removeLoadSkinListener(loadSkinListener);
        if (!recyclerViewList.isEmpty()) {
            for (RecyclerView recyclerView : recyclerViewList) {
                recyclerView.clearOnScrollListeners();
            }
        }
        mHomeAdapterList.clear();
        recyclerViewList.clear();
        mExposeRvUtilList.clear();
        // 最后调用父类的onDestroy，这会将mPresenter置为null并进行其他清理
        super.onDestroy();
    }

    @Override
    public void onItemViewVisible(boolean visible, int position) {
        if (visible && mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            for (HomeAdapter homeAdapter : mHomeAdapterList) {
                HomeCell homeCell = homeAdapter.getItemData(position);
                reportContentShowEvent(homeCell);
            }
        }
    }


    //用于埋点事件上报
    @Override
    public void onUserVisible() {
        super.onUserVisible();
        if (!recyclerViewList.isEmpty()) {
            recyclerViewList.get(0).postDelayed(new Runnable() {
                @Override
                public void run() {
                    for (RecyclerViewExposeUtil recyclerViewExposeUtil : mExposeRvUtilList) {
                        recyclerViewExposeUtil.handleCurrentVisibleItems();
                    }
                }
            }, 200);
        }
    }

    public void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);

    }

    private void hideParentLoading(){
        if(getParentFragment() instanceof HorizontalHomePlayerFragment){
            HorizontalHomePlayerFragment fragment = (HorizontalHomePlayerFragment)getParentFragment();
            fragment.hideLoading();
        }
    }

    @Override
    public void showContent(List<HomeCell> cells) {
        isShowDate = true;
        hideLoading();
        hideParentLoading();
        if (cells == null) {
            showNoNetWorkView("");
            return;
        }
        mHomeCells = checkBrandPageCell(cells);
        hideErrorLayout();
        addListView(false);
        if (!recyclerViewList.isEmpty()) {
            for (GridRecyclerView recyclerView : recyclerViewList) {
                initListDate(recyclerView);
            }
        }
        if (!mHomeRecyclerViewHelperList.isEmpty()) {
            for (int i = 0; i < mHomeRecyclerViewHelperList.size(); i++) {
                if (!brandPageCell.isEmpty() && i == 0) {
                    mHomeRecyclerViewHelperList.get(i).setDataList(brandPageCell);
                } else {
                    mHomeRecyclerViewHelperList.get(i).setDataList(cells);
                }
            }

        }

        // 这是什么玩意
        if(BuildConfig.IS_CAN_SEE_CAN_SAY_ENABLE){
            EventBus.getDefault().post(new FirstDataLoadedEvent());
        }
        mPresenter.schedulProgramListRequest(1000);
    }


    /**
     * 校验是否有品牌主页
     */
    private List<HomeCell> checkBrandPageCell(List<HomeCell> cells) {
        if (isBrandPageCell(cells)) {
            //如果是品牌主页入口类型就要从列表移除
            brandPageCell.add(cells.get(0));
            cells.remove(0);
            for (HomeCell cell : cells) {
                //因为去除了光圈组件所以所有的索引应该-1
                cell.setPositionInParent(cell.getPositionInParent() - 1);
            }
            showBrandPage();

            return cells;
        } else {
            boolean b = false;
            for (int i = 0; i < cells.size(); i++) {
                //如果入口组件配置在非第0个位置就要移除，不显示
                if (cells.get(i).itemType == ResType.HOME_ITEM_TYPE_BRAND_HOME) {
                    cells.remove(i);
                    b = true;
                }
            }
            if (b) {
                //如果进行了移除操作，就要是对索引进行重新排序
                for (int i = 0; i < cells.size(); i++) {
                    cells.get(i).setPositionInParent(i);
                }
            }
        }
        return cells;
    }

    private boolean isBrandPageCell(List<HomeCell> cells) {
        if (cells != null && !cells.isEmpty()) {
            if (cells.get(0).itemType == ResType.HOME_ITEM_TYPE_BRAND_HOME) {
                return true;
            }
        }
        return false;
    }

    private void addListView(boolean isBrandListView) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.home_date_list, null);
        GridRecyclerView recyclerView = view.findViewById(R.id.rv_home_content);
        recyclerViewList.add(recyclerView);

        mScrollLeft.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(recyclerView, -1));
        mScrollRight.setOnClickListener((v) -> ScrollingUtil.scrollListHorizontalByVoice(recyclerView, 1));

        if (!isBrandListView) {
            view.setPadding(view.getPaddingLeft(),ResUtil.getDimen(R.dimen.m15),view.getPaddingRight(),ResUtil.getDimen(R.dimen.m42));
        }
        home_root_cl.addView(view);
    }

    /**
     * 展示品牌入口数据
     */
    private void showBrandPage() {
        ConfigSettingManager.getInstance().getConfigSetting(new IConfigSettingOptionListener() {
            @Override
            public void onGetSuccess(ConfigSettingOption configSettingOption) {
                if (configSettingOption.getShowBrandEntries() != null && configSettingOption.getShowBrandEntries() == 1) {
                    //需要展示
                    addListView(true);
                } else {
                    brandPageCell.clear();
                }
            }

            @Override
            public void onGetFailure(ApiException e) {

            }
        });
    }

    @Override
    public void showLoading() {
        showOrHideLoadingView(true);
    }

    @Override
    public void hideLoading() {
        showOrHideLoadingView(false);
    }

    @SuppressLint("LongLogTag")
    private void showOrHideLoadingView(boolean isShow) {
        Log.i(TAG, "showOrHideLoadingView: " + isShow);
        ViewUtil.setViewVisibility(mPcLoadingView, isShow ? View.VISIBLE : View.GONE);
    }

    @Override
    public void showError(String error) {
        hideLoading();
        showNoNetWorkView(error);
    }

    @Override
    public void hideErrorLayout() {
        if (mHomeNoNetWorkRl != null) {
            //这里要把无网的view移除，是因为横竖屏转换的时候设置GONE不起作用，依然会显示出来。
            ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.GONE);
        }
    }

    @Override
    public void showImage(long id, String img,String title,String desc) {
        if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            for (HomeAdapter homeAdapter : mHomeAdapterList) {
                homeAdapter.updateItem(id+"", img,title,desc);
            }
        }
    }

    @SuppressLint("LongLogTag")
    public void showNoNetWorkView(String error) {
        if (mHomeAdapterList != null && !mHomeAdapterList.isEmpty()) {
            Log.i(TAG, "showNoNetWorkView RecyclerView has child!");
            return;
        }
        showOrHideLoadingView(false);
        if (mVsHomeNoNetwork != null) {
            if (null == mHomeNoNetWorkRl) {
                mHomeNoNetWorkRl = mVsHomeNoNetwork.inflate();
            }
            if (StringUtil.isNotEmpty(error)) {
                TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                tvNetworkNosign.setText(error);
            }
            // 支持点击重试
            KRadioClickRetryInter mKRadioClickRetryInter = ClazzImplUtil.getInter(
                    "KRadioClickRetryInterImpl");
            if (mKRadioClickRetryInter == null || mKRadioClickRetryInter.canRetry()) {
                ImageView ivNetworkNoSign = mHomeNoNetWorkRl.findViewById(R.id.network_nosigin);
                ivNetworkNoSign.setOnClickListener(v -> {
                    hideErrorLayout();
                    if (!OpenSDK.getInstance().isActivate()) {
                        KradioSDKManager.getInstance().addUsableObserver(() -> {
                            mPresenter.initData(zone);
                        });
                        KradioSDKManager.getInstance().initAndActivate();
                    } else {
                        mPresenter.initData(zone);
                    }
                    TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
                    String text = null;
                    if (tvNetworkNosign != null) {
                        text = tvNetworkNosign.getText().toString();
                    }
                    ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK,
                            ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
                });
            }
        }
        ViewUtil.setViewVisibility(mHomeNoNetWorkRl, View.VISIBLE);
        if (mHomeNoNetWorkRl != null) {
            TextView tvNetworkNosign = mHomeNoNetWorkRl.findViewById(R.id.tv_network_nosign);
            String text = null;
            if (tvNetworkNosign != null) {
                text = tvNetworkNosign.getText().toString();
            }
            ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, ButtonExposureOrClickReportEvent.BUTTON_ID_NETWORK_RETRY, text, getPageId(), ReportConstants.CONTROL_TYPE_SCREEN));
        }
    }

    /**
     * 需求功能点：
     *  1、当前播放内容为A，暂停时，再次点击A卡片，A可以接续播放;
     *  2、当前播放内容为A，播放期间，再次点击A卡片，不影响当前播放状态，但是会进入专辑或专题详情页。
     */
    private void handleClickCurPlayItem(HomeCell homeCell){
        boolean isPlaying = PlayerManagerHelper.getInstance().isPlaying();
        Log.i("handleClickCurPlayItem", "ComperhensiveHomeDateFragment -> handleClickCurPlayItem -> isPlaying=" + isPlaying);
        if (isPlaying){
            handleItemClickPlayingItem();
        } else {
            clickToPlay(homeCell);
        }
    }

    /**
     * 处理点击正在播放中的 item
     */
    private void handleItemClickPlayingItem(){
        int type = PlayerManager.getInstance().getCustomType();
        boolean isVideoType =
                type == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO
                || type == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM
                || type == PlayerConstants.RESOURCES_TYPE_TEMP_TASK;
        Log.i("handleClickCurPlayItem", "ComperhensiveHomeDateFragment -> handleItemClickPlayingItem -> type=" + type + ", isVideoType=" + isVideoType);
        if (isVideoType) {
//            navigationToVideoPlayer();
        } else {
            ComprehensivePlayerBar playerBar = null;
            if (getActivity() instanceof LauncherActivity){
                playerBar = ((LauncherActivity) getActivity()).getPlayerBar();
            }
            Log.i("handleClickCurPlayItem", "ComperhensiveHomeDateFragment -> handleItemClickPlayingItem -> playerBar=" + playerBar);
            PageJumper.getInstance().jumpToPlayerFragment(playerBar);
        }
    }
}