package com.kaolafm.kradio.scene.launcher;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import androidx.annotation.Nullable;
import androidx.core.app.JobIntentService;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.common.http.vehicle.KlSdkVehicle;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.BuglyUsageControlInter;
import com.kaolafm.kradio.lib.base.flavor.LocationStrategy;
import com.kaolafm.kradio.lib.location.KaoLaLocationFactory;
import com.kaolafm.kradio.lib.location.model.IKaoLaLocation;
import com.kaolafm.kradio.lib.location.model.LocationModel;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.KaolaAppConfigData;
import com.kaolafm.kradio.scene.SceneManager;
import com.kaolafm.kradio.scene.SceneReceiver;
import com.kaolafm.kradio.scene.SceneService;
import com.kaolafm.kradio.scene.launcher.event.LocationEvent;
import com.kaolafm.opensdk.OpenSDK;
import com.tencent.bugly.crashreport.CrashReport;

import org.greenrobot.eventbus.EventBus;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.ref.WeakReference;

import me.yokeyword.fragmentation.Fragmentation;
import me.yokeyword.fragmentation.helper.ExceptionHandler;

/**
 *
 */
public class InitService extends JobIntentService {

    public static final String TAG = "kradio.init";

    public static final String KEY_PROCESS = "key_process";

    private static WeakReference<InitService> initServiceRef;

    public InitService() {
        initServiceRef = new WeakReference<>(this);
    }

    @Override
    protected void onHandleWork(@Nullable Intent intent) {
        initBugly();
        initLocation();
        initSceneReceiver();
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    public static void start(Context context) {
        enqueueWork(context, InitService.class, 100, new Intent());
    }

    /**
     *
     */
    public static void getLocation() {
        if (initServiceRef == null || initServiceRef.get() == null){
            new InitService();
        }
        initServiceRef.get().initLocation();
    }


    /***************************************************************************************************************/

    private final IKaoLaLocation.IKaoLaLocationListener iKaoLaLocationListener = new MKaoLaLocationListener();


    private void initLocation() {
        //fixed 应用初始化时，还没有申请到权限，因此会导致定位失败，首页显示"本地广播"错误
        //高德地图发送消息到所在线程的messagequeue,intentservice的MessageQueue,会在任务执行完后quit().
        //故为其创建独立HandlerThread.
        HandlerThread thread = new HandlerThread("kradio.map");
        thread.start();
        Handler handler = new Handler(thread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                KaoLaLocationFactory.getLocationManager(getLocationType()).addLocationListener(iKaoLaLocationListener);
            }
        };
        handler.sendEmptyMessage(0);
    }

    private String getLocationType() {
        LocationStrategy locationStrategy = ClazzImplUtil.getInter("LocationStrategyImpl");
        return locationStrategy != null ? locationStrategy.locationType() : KaoLaLocationFactory.CUSTOMIZED_API;
    }

    /***************************************************************************************************************/

    /**
     * 初始化Bugly数据上报
     */
    private void initBugly() {
        Log.i(TAG, "init Bugly");
        BuglyUsageControlInter buglyUsageControlInter = ClazzImplUtil.getInter("BuglyUsageControlInterImpl");
        if (null != buglyUsageControlInter && buglyUsageControlInter.disableBugly()) {
            Log.i(TAG, "Bugly is disabled.");
            return;
        }

        Context context = getApplicationContext();
        // 获取当前包名
        String packageName = context.getPackageName();
        // 获取当前进程名
        String processName = getProcessName(android.os.Process.myPid());
        // 设置是否为上报进程
        CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(context);
        String version = KlSdkVehicle.getInstance().getVersionName(context);
        if (!TextUtils.isEmpty(version)) {
            version = version.substring(0, version.lastIndexOf('.'));
        }
        strategy.setAppVersion(version);
        strategy.setAppChannel(KaolaAppConfigData.getInstance().getChannel());
        strategy.setUploadProcess(processName == null || processName.equals(packageName));
        // 如果通过“AndroidManifest.xml”来配置APP信息，初始化方法如下
        CrashReport.initCrashReport(context, strategy);
        Log.i(TAG, "Bugly init success.");
    }

    /***************************************************************************************************************/
    /**
     * 获取进程号对应的进程名
     *
     * @param pid 进程号
     * @return 进程名
     */
    private String getProcessName(int pid) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new FileReader("/proc/" + pid + "/cmdline"));
            String processName = reader.readLine();
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim();
            }
            return processName;
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException exception) {
                exception.printStackTrace();
            }
        }
        return null;
    }

    /***************************************************************************************************************/
    private void initFragManagement() {
        Fragmentation.builder()
                // 设置 栈视图 模式为 （默认）悬浮球模式   SHAKE: 摇一摇唤出  NONE：隐藏， 仅在Debug环境生效
                .stackViewMode(Fragmentation.BUBBLE)
                // 实际场景建议.debug(BuildConfig.DEBUG)
                .debug(true)
                //可以获取到{@link me.yokeyword.fragmentation.exception.AfterSaveStateTransactionWarning}
                //在遇到After onSaveInstanceState时，不会抛出异常，会回调到下面的ExceptionHandler
                .handleException(new ExceptionHandler() {
                    @Override
                    public void onException(Exception e) {
                        // 记录Fragment事务异常，便于调试
                        android.util.Log.e("Fragmentation", "Fragment transaction exception occurred", e);

                        // 上报异常信息到崩溃收集系统
                        // 以Bugtags为例子: 把捕获到的 Exception 传到 Bugtags 后台。
                        // Bugtags.sendException(e);

                        // 可以在这里添加其他异常处理逻辑，比如：
                        // 1. 记录到本地日志文件
                        // 2. 上报到服务器
                        // 3. 显示用户友好的错误提示
                    }
                })
                .install();
    }

    /***************************************************************************************************************/
    private SceneReceiver mSceneReceiver;

    /**
     * 初始化场景所需类
     */
    private void initSceneReceiver() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(SceneManager.ACTION_SCENE_CMD);
        if (mSceneReceiver == null) {
            mSceneReceiver = new SceneReceiver();
        }
        getApplication().registerReceiver(mSceneReceiver, filter);
        //
        AppManager.getInstance().registerExitListener(new MExitListener(this, mSceneReceiver));
    }

    /***************************************************************************************************************/

    private void killServices() {
        Application context = AppDelegate.getInstance().getContext();
        if (context != null) {
            // Intent intentStatistics = new Intent(context, com.kaolafm.sdk.core.statistics.StatisticsService.class);//open sdk
            Intent intentScene = new Intent(context, SceneService.class);//open sdk
            //Intent intentDaemon = new Intent(context, cn.jpush.android.service.DaemonService.class);//open sdk
            // Intent intentPush = new Intent(context, cn.jpush.android.service.PushService.class);//open sdk
            // context.stopService(intentStatistics);
            context.stopService(intentScene);
            // context.stopService(intentDaemon);
            // context.stopService(intentPush);
        }
    }
    /***************************************************************************************************************/

    private static class MExitListener implements AppManager.ExitListener{

        private WeakReference<InitService> initServiceRef;
        private WeakReference<SceneReceiver> sceneReceiverRef;

        public MExitListener(InitService initService, SceneReceiver sceneReceiver) {
            this.initServiceRef = new WeakReference<>(initService);
            this.sceneReceiverRef = new WeakReference<>(sceneReceiver);
        }

        @Override
        public void onAppExit() {
            Log.i(TAG, "onAppExit: onAppExit");
            if (sceneReceiverRef != null && sceneReceiverRef.get() != null) {
                // 此处会出现java.lang.IllegalArgumentException: Receiver not registered: com.kaolafm.launcher.scene.SceneReceiver@52d4f26异常
                try {
                    if (initServiceRef != null && initServiceRef.get() != null) {
                        initServiceRef.get().getApplication().unregisterReceiver(sceneReceiverRef.get());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //关闭服务
                if (initServiceRef != null && initServiceRef.get() != null) {
                    initServiceRef.get().killServices();
                    initServiceRef.get().stopSelf();
                }

            }
        }
    }

    private static class MKaoLaLocationListener implements IKaoLaLocation.IKaoLaLocationListener{

        String latitude, longitude;
        @Override
        public void locationChange(LocationModel location) {
            if (location != null) {
                Log.i(TAG + "location", "位置变化监听: Latitude = " + location.getLatitude() + ", Longitude = " + location.getLongitude());
                latitude = String.valueOf(location.getLatitude());
                longitude = String.valueOf(location.getLongitude());
                try {
                    OpenSDK.getInstance().getmEngine().setLocation(longitude, latitude);

                    KaolaAppConfigData.getInstance().setLat(latitude);
                    KaolaAppConfigData.getInstance().setLng(longitude);
                    EventBus.getDefault().postSticky(new LocationEvent());
                } catch (Exception e) {
                    Log.e(TAG + "location", "Exception:" + e);
                }
            }
        }
    }
}
